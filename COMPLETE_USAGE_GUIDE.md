# Complete Usage Guide: Decentralized Multi-Robot Scheduling with MiniGrid Simulation

## Overview

This guide shows you how to use the complete decentralized multi-robot scheduling system, from training models to running grid-based simulations. The system includes:

1. **Decentralized Training**: Train individual robot networks using your existing infrastructure
2. **Grid Simulation**: Test trained models in a MiniGrid environment where N robots cooperatively visit M goals
3. **Comprehensive Analysis**: Compare centralized vs decentralized approaches

## Quick Start (Complete Workflow)

### Step 1: Train a Decentralized Model

```bash
# Train decentralized model using your existing data
python3 decentralized_multi_objective_train.py \
    --path-to-train ./problem_instances/constraints \
    --num-robots 2 \
    --train-start-no 1 \
    --train-end-no 100 \
    --steps 500 \
    --alpha 0.5 \
    --beta 0.5 \
    --cpsave ./cp_decentralized
```

### Step 2: Test the Basic Grid Simulation

```bash
# Run simple demo (works without trained models)
python3 simple_grid_demo.py
```

### Step 3: Run Full Simulation with Trained Model

```bash
# Run simulation with your trained model
python3 run_decentralized_simulation.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --grid-size 10 \
    --num-robots 2 \
    --num-goals 5 \
    --communication-rounds 2
```

### Step 4: Run Batch Experiments

```bash
# Run comprehensive evaluation across multiple scenarios
python3 batch_grid_simulation.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --num-runs 5
```

## Detailed Workflow

### 1. Training Phase

#### Option A: Train Decentralized Model Only
```bash
python3 decentralized_multi_objective_train.py \
    --path-to-train ./problem_instances/constraints \
    --num-robots 2 \
    --steps 500 \
    --alpha 0.5 \
    --beta 0.5
```

#### Option B: Train Both Centralized and Decentralized (for comparison)
```bash
# Train centralized model
python3 multi_objective_train.py \
    --path-to-train ./problem_instances/constraints \
    --num-robots 2 \
    --steps 500 \
    --alpha 0.5 \
    --beta 0.5

# Train decentralized model
python3 decentralized_multi_objective_train.py \
    --path-to-train ./problem_instances/constraints \
    --num-robots 2 \
    --steps 500 \
    --alpha 0.5 \
    --beta 0.5
```

### 2. Testing Phase

#### Test Models on Original Problem Format
```bash
# Test centralized model
python3 test_models_csv_format.py \
    --model-path ./cp_multi_obj/checkpoint_00500.tar \
    --test-data ./problem_instances_test/constraints \
    --output-dir ./csv_outputs_centralized

# Test decentralized model
python3 test_models_csv_format.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --test-data ./problem_instances_test/constraints \
    --output-dir ./csv_outputs_decentralized
```

#### Test Models in Grid Simulation
```bash
# Single simulation run
python3 run_decentralized_simulation.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --grid-size 12 \
    --num-robots 3 \
    --num-goals 8 \
    --max-steps 200 \
    --communication-rounds 2 \
    --save-frames

# Batch experiments with different scenarios
python3 batch_grid_simulation.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --num-runs 10 \
    --output-dir ./grid_simulation_results
```

### 3. Comparison Phase

#### Complete Centralized vs Decentralized Comparison
```bash
python3 compare_centralized_decentralized.py \
    --train-data ./problem_instances/constraints \
    --test-data ./problem_instances_test/constraints \
    --num-robots 2 \
    --steps 200 \
    --output-dir ./comparison_results
```

## Configuration Options

### Training Parameters

| Parameter | Description | Default | Recommended Range |
|-----------|-------------|---------|-------------------|
| `--num-robots` | Number of robots | 2 | 2-5 |
| `--steps` | Training steps | 500 | 200-1000 |
| `--alpha` | Makespan weight | 0.5 | 0.0-1.0 |
| `--beta` | Balance weight | 0.5 | 0.0-1.0 |
| `--lr` | Learning rate | 1e-4 | 1e-5 to 1e-3 |
| `--batch-size` | Batch size per robot | 8 | 4-16 |

### Simulation Parameters

| Parameter | Description | Default | Recommended Range |
|-----------|-------------|---------|-------------------|
| `--grid-size` | L×L grid size | 10 | 6-15 |
| `--num-robots` | Number of robots (N) | 2 | 2-5 |
| `--num-goals` | Number of goals (M) | 5 | 3-12 |
| `--communication-rounds` | Communication rounds | 2 | 1-4 |
| `--max-steps` | Maximum simulation steps | 200 | 100-500 |

## Expected Results

### Training Results
```
[Step  500] Total Loss: 0.234567, Avg Reward: 12.34, Time: 1.23s
Decentralized checkpoint saved: ./cp_decentralized/decentralized_checkpoint_00500.tar
```

### Simulation Results
```
=== SIMULATION RESULTS ===
Goals completed: 5/5 (100.0%)
Total steps: 47
Total distance traveled: 23
Workload balance (std): 1.414
Robot distances: [11, 12]
Average goal completion time: 35.2 steps
```

### Batch Experiment Results
```
Scenario: Medium_Balanced
  Completion Rate: 92.0% ± 8.5%
  Average Steps: 156.3 ± 23.1
  Workload Balance: 2.14 ± 0.67
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Training Issues
```bash
# Error: "No training data collected"
# Solution: Check that problem instance files exist
ls ./problem_instances/constraints/*_dur.txt

# Error: "CUDA out of memory"
# Solution: Use CPU or reduce batch size
python3 decentralized_multi_objective_train.py --device cpu --batch-size 4
```

#### 2. Simulation Issues
```bash
# Error: "ValueError: not enough values to unpack"
# Solution: This was fixed in the latest version. Make sure you're using the updated files.

# Error: "Model loading failed"
# Solution: Check model path and ensure it's a decentralized model
python3 test_simulation_fix.py  # Run this to verify the fix
```

#### 3. Visualization Issues
```bash
# Error: "No display available"
# Solution: Use headless mode
python3 run_decentralized_simulation.py --no-visualization --model-path MODEL_PATH

# Or run the simple demo without visualization
python3 simple_grid_demo.py
```

### Performance Optimization

#### For Faster Training
```bash
# Reduce training data size
python3 decentralized_multi_objective_train.py \
    --train-end-no 50 \
    --steps 200

# Use smaller batch size
python3 decentralized_multi_objective_train.py \
    --batch-size 4
```

#### For Faster Simulation
```bash
# Reduce communication rounds
python3 run_decentralized_simulation.py \
    --communication-rounds 1 \
    --model-path MODEL_PATH

# Use smaller grid
python3 run_decentralized_simulation.py \
    --grid-size 8 \
    --num-goals 4 \
    --model-path MODEL_PATH
```

## File Structure Summary

```
├── Core Training & Testing
│   ├── decentralized_multi_objective_train.py    # Train decentralized models
│   ├── test_models_csv_format.py                 # Test both model types
│   └── compare_centralized_decentralized.py      # Complete comparison
│
├── Grid Simulation
│   ├── minigrid_environment.py                   # Grid world environment
│   ├── run_decentralized_simulation.py           # Main simulation runner
│   ├── batch_grid_simulation.py                  # Batch experiments
│   └── simple_grid_demo.py                       # Simple demo
│
├── Enhanced Models
│   └── hetnet.py                                 # Decentralized network architectures
│
└── Documentation
    ├── COMPLETE_USAGE_GUIDE.md                   # This file
    ├── MINIGRID_SIMULATION_README.md             # Detailed simulation docs
    └── DECENTRALIZED_USAGE_GUIDE.md              # Decentralized model docs
```

## Next Steps

1. **Start with Simple Demo**: Run `python3 simple_grid_demo.py` to verify everything works
2. **Train Your First Model**: Use the training command above with your data
3. **Run Grid Simulation**: Test your trained model in the grid environment
4. **Analyze Results**: Use batch experiments to evaluate performance
5. **Compare Approaches**: Run the complete comparison to see centralized vs decentralized trade-offs

## Research Applications

This framework enables research in:
- **Decentralized Multi-Agent Systems**: Study coordination without central control
- **Communication-Limited Environments**: Test performance with limited robot communication
- **Scalability Analysis**: Evaluate how performance changes with team size
- **Real-World Deployment**: Bridge the gap between simulation and physical robots

The system provides a complete pipeline from training to evaluation, making it easy to experiment with different decentralized coordination strategies.

# MiniGrid Decentralized Multi-Robot Simulation

## Overview

This simulation framework implements a grid-based environment where N autonomous robots cooperatively discover and visit M goals using your trained decentralized scheduling models. The framework operates within an L×L grid world and provides visualization, analysis, and batch testing capabilities.

## Features

- **Grid-based Environment**: Customizable L×L grid world with obstacles, robots, and goals
- **Decentralized Decision Making**: Uses your trained decentralized models for robot coordination
- **Real-time Visualization**: Interactive matplotlib-based visualization with robot paths and task assignments
- **Batch Experiments**: Run multiple scenarios to evaluate model performance
- **Comprehensive Analysis**: Automatic generation of performance metrics and visualizations
- **Communication Modeling**: Configurable communication rounds between robots

## File Structure

```
├── minigrid_environment.py          # Core grid world environment and visualization
├── run_decentralized_simulation.py  # Main simulation runner with trained models
├── batch_grid_simulation.py         # Batch experiment runner and analysis
├── example_simulation.py            # Simple examples and demos
└── MINIGRID_SIMULATION_README.md    # This file
```

## Quick Start

### 1. Train a Decentralized Model (if not already done)

```bash
python decentralized_multi_objective_train.py \
    --path-to-train ./problem_instances/constraints \
    --num-robots 2 \
    --steps 500 \
    --alpha 0.5 \
    --beta 0.5
```

### 2. Run a Single Simulation

```bash
python run_decentralized_simulation.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --grid-size 10 \
    --num-robots 2 \
    --num-goals 5 \
    --communication-rounds 2
```

### 3. Run Batch Experiments

```bash
python batch_grid_simulation.py \
    --model-path ./cp_decentralized/decentralized_checkpoint_00500.tar \
    --num-runs 5
```

### 4. Try the Example Demo

```bash
python example_simulation.py
```

## Detailed Usage

### Single Simulation

The main simulation script provides comprehensive options:

```bash
python run_decentralized_simulation.py \
    --model-path MODEL_PATH \
    --grid-size 12 \
    --num-robots 3 \
    --num-goals 8 \
    --max-steps 200 \
    --communication-rounds 2 \
    --save-frames \
    --device cpu
```

**Parameters:**
- `--model-path`: Path to your trained decentralized model checkpoint
- `--grid-size`: Size of the L×L grid (default: 10)
- `--num-robots`: Number of robots N (default: 2)
- `--num-goals`: Number of goals M (default: 5)
- `--max-steps`: Maximum simulation steps (default: 200)
- `--communication-rounds`: Number of communication rounds between robots (default: 2)
- `--save-frames`: Save visualization frames as PNG files
- `--no-visualization`: Disable real-time visualization
- `--device`: PyTorch device (cpu/cuda)

### Batch Experiments

Run systematic evaluations across multiple scenarios:

```bash
python batch_grid_simulation.py \
    --model-path MODEL_PATH \
    --scenarios-file custom_scenarios.json \
    --num-runs 10 \
    --output-dir ./results
```

**Custom Scenarios Format (JSON):**
```json
[
  {
    "name": "Small_Test",
    "grid_size": 8,
    "num_robots": 2,
    "num_goals": 4,
    "max_steps": 150,
    "communication_rounds": 2
  },
  {
    "name": "Large_Test",
    "grid_size": 15,
    "num_robots": 4,
    "num_goals": 12,
    "max_steps": 300,
    "communication_rounds": 3
  }
]
```

## Environment Details

### Grid World Components

1. **Grid Cells**:
   - Empty (0): Navigable space
   - Obstacle (1): Blocked cells
   - Goal (2): Target locations to visit
   - Robot (3): Current robot positions

2. **Robot Actions**:
   - 0: Stay in place
   - 1: Move up
   - 2: Move right
   - 3: Move down
   - 4: Move left

3. **Observations**:
   - Local grid view (configurable vision range)
   - Visible goals and their distances
   - Visible other robots
   - Current task assignments

### Decentralized Decision Process

1. **Problem Conversion**: Grid state → scheduling problem format
2. **Local Evaluation**: Each robot evaluates available goals using trained model
3. **Communication**: Robots share information for specified rounds
4. **Conflict Resolution**: Robot with highest confidence executes preferred action
5. **Path Planning**: Simple greedy path planning to assigned goals
6. **Execution**: Robots move according to their plans

## Performance Metrics

The simulation tracks comprehensive performance metrics:

### Primary Metrics
- **Completion Rate**: Percentage of goals successfully visited
- **Total Steps**: Number of simulation steps to completion
- **Workload Balance**: Standard deviation of distances traveled by robots
- **Total Distance**: Sum of all robot movements

### Advanced Metrics
- **Goal Completion Times**: When each goal was reached
- **Average Completion Time**: Mean time to reach goals
- **Robot Efficiency**: Individual robot performance
- **Communication Effectiveness**: Impact of communication rounds

## Visualization Features

### Real-time Display
- **Grid Layout**: Clear visualization of the environment
- **Robot Paths**: Dotted lines showing movement history
- **Task Assignments**: Lines connecting robots to assigned goals
- **Goal Status**: Different colors for visited/unvisited goals
- **Legend**: Clear identification of all elements

### Saved Outputs
- **Frame Sequences**: PNG files for each simulation step
- **Performance Plots**: Automatic generation of analysis charts
- **Summary Statistics**: CSV files with detailed metrics

## Analysis and Results

### Automatic Analysis

Batch experiments automatically generate:

1. **Completion Rate Analysis**: Box plots by scenario
2. **Efficiency Analysis**: Steps vs completion rate scatter plots
3. **Workload Balance**: Distribution analysis across scenarios
4. **Scalability Analysis**: Heatmaps for different robot/goal combinations
5. **Communication Impact**: Performance vs communication rounds

### Example Results

```
=== SIMULATION RESULTS ===
Goals completed: 5/5 (100.0%)
Total steps: 47
Total distance traveled: 23
Workload balance (std): 1.414
Robot distances: [11, 12]
Average goal completion time: 35.2 steps
```

## Integration with Your Models

### Model Compatibility

The simulation works with models trained using:
- `decentralized_multi_objective_train.py`
- Multi-objective optimization (α makespan + β workload balance)
- Your existing network architectures (`DecentralizedScheduleNet`, `MultiRobotDecentralizedSystem`)

### Data Flow

1. **Grid State** → **Scheduling Problem**: Convert positions and goals to duration matrices
2. **Scheduling Problem** → **Neural Network**: Use your trained models for decisions
3. **Network Output** → **Robot Actions**: Convert Q-values to movement commands
4. **Actions** → **Grid Update**: Execute movements and update environment

## Customization Options

### Environment Customization

```python
# Modify obstacle density
num_obstacles = grid_size // 2  # More obstacles

# Custom robot/goal placement
def custom_initialization(self):
    # Place robots in corners
    self.robot_positions = [(0, 0), (grid_size-1, grid_size-1)]
    # Place goals in center
    center = grid_size // 2
    self.goal_positions = [(center, center)]
```

### Communication Topology

```python
# Modify communication graph in MultiRobotDecentralizedSystem
def _build_communication_graph(self):
    # Ring topology
    graph = {}
    for i in range(self.num_robots):
        neighbors = [(i-1) % self.num_robots, (i+1) % self.num_robots]
        graph[i] = neighbors
    return graph
```

### Visualization Customization

```python
# Custom colors and styles
robot_colors = ['red', 'blue', 'green', 'orange', 'purple']
goal_color = 'gold'
obstacle_color = 'gray'
```

## Troubleshooting

### Common Issues

1. **Model Loading Errors**:
   - Ensure model was trained with `decentralized_multi_objective_train.py`
   - Check that `num_robots` matches the model

2. **Visualization Issues**:
   - Use `--no-visualization` for headless environments
   - Install matplotlib with GUI backend for interactive display

3. **Performance Issues**:
   - Reduce `communication_rounds` for faster execution
   - Use smaller grid sizes for testing

4. **Memory Issues**:
   - Reduce batch size in experiments
   - Use CPU instead of GPU for large grids

### Debug Mode

```python
# Enable verbose logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Extensions

### Planned Features
- **Dynamic Obstacles**: Moving obstacles during simulation
- **Heterogeneous Robots**: Different robot capabilities
- **Multi-objective Visualization**: Real-time objective tracking
- **3D Environments**: Extension to 3D grid worlds
- **Real Robot Integration**: ROS interface for physical robots

### Research Applications
- **Swarm Intelligence**: Large-scale robot coordination
- **Emergency Response**: Search and rescue scenarios
- **Warehouse Automation**: Automated picking and delivery
- **Environmental Monitoring**: Distributed sensor networks

## Citation

If you use this simulation framework in your research, please cite:

```bibtex
@misc{decentralized_grid_sim,
  title={Decentralized Multi-Robot Grid Simulation Framework},
  author={Your Name},
  year={2024},
  note={Grid-based simulation for decentralized multi-robot task scheduling}
}
```

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
batch_grid_simulation.py

Run batch simulations to evaluate decentralized models across different scenarios.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List
import json

from run_decentralized_simulation import DecentralizedGridSimulator


def run_batch_experiments(model_path: str, device, scenarios: List[Dict], 
                         num_runs: int = 5) -> pd.DataFrame:
    """
    Run batch experiments across different scenarios.
    
    Args:
        model_path: Path to trained model
        device: PyTorch device
        scenarios: List of scenario configurations
        num_runs: Number of runs per scenario
        
    Returns:
        DataFrame with all results
    """
    results = []
    
    for scenario_id, scenario in enumerate(scenarios):
        print(f"\nRunning scenario {scenario_id + 1}/{len(scenarios)}: {scenario['name']}")
        print(f"Grid: {scenario['grid_size']}×{scenario['grid_size']}, "
              f"Robots: {scenario['num_robots']}, Goals: {scenario['num_goals']}")
        
        for run in range(num_runs):
            print(f"  Run {run + 1}/{num_runs}")
            
            # Set different seed for each run
            np.random.seed(42 + run)
            
            try:
                # Create simulator
                simulator = DecentralizedGridSimulator(
                    model_path, device, 
                    scenario['grid_size'], 
                    scenario['num_robots'], 
                    scenario['num_goals']
                )
                
                # Run simulation
                stats = simulator.run_simulation(
                    max_steps=scenario.get('max_steps', 200),
                    visualization=False,
                    communication_rounds=scenario.get('communication_rounds', 2)
                )
                
                # Record results
                result = {
                    'scenario_id': scenario_id,
                    'scenario_name': scenario['name'],
                    'run': run,
                    'grid_size': scenario['grid_size'],
                    'num_robots': scenario['num_robots'],
                    'num_goals': scenario['num_goals'],
                    'communication_rounds': scenario.get('communication_rounds', 2),
                    **stats
                }
                results.append(result)
                
                print(f"    Completed: {stats['goals_completed']}/{scenario['num_goals']} goals, "
                      f"{stats['total_steps']} steps, balance: {stats['workload_balance']:.3f}")
                
            except Exception as e:
                print(f"    Error in run {run}: {e}")
                # Record failed run
                result = {
                    'scenario_id': scenario_id,
                    'scenario_name': scenario['name'],
                    'run': run,
                    'grid_size': scenario['grid_size'],
                    'num_robots': scenario['num_robots'],
                    'num_goals': scenario['num_goals'],
                    'communication_rounds': scenario.get('communication_rounds', 2),
                    'total_steps': np.nan,
                    'goals_completed': 0,
                    'completion_rate': 0.0,
                    'total_distance': np.nan,
                    'workload_balance': np.nan,
                    'average_completion_time': np.nan
                }
                results.append(result)
    
    return pd.DataFrame(results)


def analyze_results(df: pd.DataFrame, output_dir: str):
    """Analyze and visualize batch experiment results."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Summary statistics
    summary = df.groupby(['scenario_name', 'grid_size', 'num_robots', 'num_goals']).agg({
        'completion_rate': ['mean', 'std'],
        'total_steps': ['mean', 'std'],
        'workload_balance': ['mean', 'std'],
        'total_distance': ['mean', 'std'],
        'average_completion_time': ['mean', 'std']
    }).round(3)
    
    summary.to_csv(os.path.join(output_dir, 'batch_summary.csv'))
    print("\nSummary statistics:")
    print(summary)
    
    # Create visualizations
    plt.style.use('default')
    
    # 1. Completion rate by scenario
    plt.figure(figsize=(12, 6))
    sns.boxplot(data=df, x='scenario_name', y='completion_rate')
    plt.xticks(rotation=45, ha='right')
    plt.title('Task Completion Rate by Scenario')
    plt.ylabel('Completion Rate')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'completion_rate_by_scenario.png'), dpi=300)
    plt.close()
    
    # 2. Steps vs completion rate
    plt.figure(figsize=(10, 6))
    for scenario in df['scenario_name'].unique():
        scenario_data = df[df['scenario_name'] == scenario]
        plt.scatter(scenario_data['total_steps'], scenario_data['completion_rate'], 
                   label=scenario, alpha=0.7)
    plt.xlabel('Total Steps')
    plt.ylabel('Completion Rate')
    plt.title('Efficiency: Steps vs Completion Rate')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'efficiency_analysis.png'), dpi=300)
    plt.close()
    
    # 3. Workload balance analysis
    plt.figure(figsize=(12, 6))
    sns.boxplot(data=df, x='scenario_name', y='workload_balance')
    plt.xticks(rotation=45, ha='right')
    plt.title('Workload Balance by Scenario')
    plt.ylabel('Workload Balance (Standard Deviation)')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'workload_balance_by_scenario.png'), dpi=300)
    plt.close()
    
    # 4. Scalability analysis (if multiple robot/goal configurations)
    if len(df['num_robots'].unique()) > 1 or len(df['num_goals'].unique()) > 1:
        plt.figure(figsize=(12, 8))
        
        # Create pivot table for heatmap
        pivot_completion = df.groupby(['num_robots', 'num_goals'])['completion_rate'].mean().unstack()
        pivot_steps = df.groupby(['num_robots', 'num_goals'])['total_steps'].mean().unstack()
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Completion rate heatmap
        sns.heatmap(pivot_completion, annot=True, fmt='.2f', cmap='YlOrRd', ax=ax1)
        ax1.set_title('Average Completion Rate')
        ax1.set_xlabel('Number of Goals')
        ax1.set_ylabel('Number of Robots')
        
        # Steps heatmap
        sns.heatmap(pivot_steps, annot=True, fmt='.0f', cmap='YlOrRd', ax=ax2)
        ax2.set_title('Average Steps to Completion')
        ax2.set_xlabel('Number of Goals')
        ax2.set_ylabel('Number of Robots')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'scalability_analysis.png'), dpi=300)
        plt.close()
    
    # 5. Communication rounds impact (if varied)
    if len(df['communication_rounds'].unique()) > 1:
        plt.figure(figsize=(10, 6))
        comm_analysis = df.groupby('communication_rounds').agg({
            'completion_rate': 'mean',
            'total_steps': 'mean',
            'workload_balance': 'mean'
        })
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        axes[0].plot(comm_analysis.index, comm_analysis['completion_rate'], 'o-')
        axes[0].set_title('Completion Rate vs Communication Rounds')
        axes[0].set_xlabel('Communication Rounds')
        axes[0].set_ylabel('Completion Rate')
        axes[0].grid(True, alpha=0.3)
        
        axes[1].plot(comm_analysis.index, comm_analysis['total_steps'], 'o-')
        axes[1].set_title('Steps vs Communication Rounds')
        axes[1].set_xlabel('Communication Rounds')
        axes[1].set_ylabel('Total Steps')
        axes[1].grid(True, alpha=0.3)
        
        axes[2].plot(comm_analysis.index, comm_analysis['workload_balance'], 'o-')
        axes[2].set_title('Workload Balance vs Communication Rounds')
        axes[2].set_xlabel('Communication Rounds')
        axes[2].set_ylabel('Workload Balance')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'communication_impact.png'), dpi=300)
        plt.close()
    
    print(f"\nAnalysis complete. Results saved to: {output_dir}")


def create_default_scenarios() -> List[Dict]:
    """Create default test scenarios."""
    scenarios = [
        {
            'name': 'Small_Balanced',
            'grid_size': 8,
            'num_robots': 2,
            'num_goals': 4,
            'max_steps': 150,
            'communication_rounds': 2
        },
        {
            'name': 'Medium_Balanced',
            'grid_size': 10,
            'num_robots': 3,
            'num_goals': 6,
            'max_steps': 200,
            'communication_rounds': 2
        },
        {
            'name': 'Large_Sparse',
            'grid_size': 12,
            'num_robots': 2,
            'num_goals': 8,
            'max_steps': 250,
            'communication_rounds': 2
        },
        {
            'name': 'Dense_Robots',
            'grid_size': 10,
            'num_robots': 4,
            'num_goals': 6,
            'max_steps': 200,
            'communication_rounds': 2
        },
        {
            'name': 'Limited_Communication',
            'grid_size': 10,
            'num_robots': 3,
            'num_goals': 6,
            'max_steps': 200,
            'communication_rounds': 1
        },
        {
            'name': 'High_Communication',
            'grid_size': 10,
            'num_robots': 3,
            'num_goals': 6,
            'max_steps': 200,
            'communication_rounds': 3
        }
    ]
    return scenarios


def main():
    parser = argparse.ArgumentParser(description="Run batch grid simulations")
    parser.add_argument("--model-path", required=True, help="Path to trained decentralized model")
    parser.add_argument("--scenarios-file", help="JSON file with custom scenarios")
    parser.add_argument("--num-runs", type=int, default=5, help="Number of runs per scenario")
    parser.add_argument("--output-dir", default="./batch_simulation_results", help="Output directory")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    
    args = parser.parse_args()
    
    # Load scenarios
    if args.scenarios_file and os.path.exists(args.scenarios_file):
        with open(args.scenarios_file, 'r') as f:
            scenarios = json.load(f)
        print(f"Loaded {len(scenarios)} scenarios from {args.scenarios_file}")
    else:
        scenarios = create_default_scenarios()
        print(f"Using {len(scenarios)} default scenarios")
    
    print("Batch Grid Simulation Evaluation")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Scenarios: {len(scenarios)}")
    print(f"Runs per scenario: {args.num_runs}")
    print(f"Total experiments: {len(scenarios) * args.num_runs}")
    
    # Run experiments
    import torch
    device = torch.device(args.device)
    
    results_df = run_batch_experiments(args.model_path, device, scenarios, args.num_runs)
    
    # Save raw results
    os.makedirs(args.output_dir, exist_ok=True)
    results_df.to_csv(os.path.join(args.output_dir, 'batch_results.csv'), index=False)
    
    # Analyze results
    analyze_results(results_df, args.output_dir)
    
    # Print final summary
    print("\n" + "=" * 50)
    print("BATCH SIMULATION COMPLETE")
    print("=" * 50)
    
    overall_stats = {
        'total_experiments': len(results_df),
        'successful_runs': len(results_df[results_df['completion_rate'] > 0]),
        'average_completion_rate': results_df['completion_rate'].mean(),
        'average_steps': results_df['total_steps'].mean(),
        'average_workload_balance': results_df['workload_balance'].mean()
    }
    
    for key, value in overall_stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.3f}")
        else:
            print(f"{key}: {value}")
    
    print(f"\nDetailed results saved to: {args.output_dir}")


if __name__ == "__main__":
    main()

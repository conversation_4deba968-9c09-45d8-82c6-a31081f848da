#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
decentralized_multi_objective_train.py

Decentralized multi-objective training script for SSAN.
Each robot has its own network and learns from local decisions with limited communication.
"""

import os
import sys
import argparse
import time
import copy
import numpy as np
import torch
import torch.nn.functional as F
import torch.nn.utils as utils
from torch.optim.lr_scheduler import ReduceLROnPlateau
import matplotlib.pyplot as plt

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper, Transition
from hetnet import DecentralizedScheduleNet, MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance_from_env


def collect_decentralized_data(data_dir, start_no, end_no, num_robots, alpha=0.5, beta=0.5):
    """
    Collect training data for decentralized learning.
    Each robot learns from its own decisions in the optimal trajectory.
    """
    robot_memories = [[] for _ in range(num_robots)]
    
    for inst_no in range(start_no, end_no + 1):
        fname = os.path.join(data_dir, f"{inst_no:05d}")
        
        if not os.path.isfile(f"{fname}_dur.txt"):
            continue
        
        try:
            env = SchedulingEnv(fname)
            env.set_multi_objective_params(alpha=alpha, beta=beta)
            
            # Load optimal solution
            optimals = []
            solutions_dir = data_dir.replace("/constraints", "/solutions")
            solution_prefix = os.path.join(solutions_dir, f"{inst_no:05d}")

            for i in range(num_robots):
                opt_file = f"{solution_prefix}_{i}.txt"
                if os.path.isfile(opt_file):
                    optimals.append(np.loadtxt(opt_file, dtype=np.int32))
                else:
                    continue

            optimalw_file = f"{solution_prefix}_w.txt"
            if not os.path.isfile(optimalw_file):
                continue
            
            optimalw = np.loadtxt(optimalw_file, dtype=np.int32)
            
            # Track states for each robot's perspective
            robot_states = [[] for _ in range(num_robots)]
            robot_actions = [[] for _ in range(num_robots)]
            robot_rewards = [[] for _ in range(num_robots)]
            
            # Execute optimal trajectory and collect robot-specific data
            for i in range(env.num_tasks):
                task = optimalw[i]
                robot = None
                for r in range(num_robots):
                    if len(optimals) > r and task in optimals[r]:
                        robot = r
                        break
                
                if robot is None:
                    robot = 0
                
                # Store state before action for the acting robot
                robot_states[robot].append({
                    'graph': copy.deepcopy(env.halfDG),
                    'partials': copy.deepcopy(env.partials),
                    'partialw': copy.deepcopy(env.partialw),
                    'locs': env.loc,
                    'durs': env.dur
                })
                
                # Execute action
                success, reward, done_flag = env.insert_robot(task, robot)
                if not success:
                    break
                
                # Store action and reward for the acting robot
                robot_actions[robot].append(task)
                robot_rewards[robot].append(reward)
                
                # Store next state
                robot_states[robot].append({
                    'graph': copy.deepcopy(env.halfDG),
                    'partials': copy.deepcopy(env.partials),
                    'partialw': copy.deepcopy(env.partialw),
                    'locs': env.loc,
                    'durs': env.dur
                })
            
            # Create transitions for each robot
            for robot_id in range(num_robots):
                for j in range(len(robot_actions[robot_id])):
                    if j < len(robot_states[robot_id]) - 1:
                        transition = Transition(
                            curr_g=robot_states[robot_id][j]['graph'],
                            curr_partials=robot_states[robot_id][j]['partials'],
                            curr_partialw=robot_states[robot_id][j]['partialw'],
                            locs=robot_states[robot_id][j]['locs'],
                            durs=robot_states[robot_id][j]['durs'],
                            act_task=robot_actions[robot_id][j],
                            act_robot=robot_id,
                            reward_n=robot_rewards[robot_id][j],
                            next_g=robot_states[robot_id][j+1]['graph'],
                            next_partials=robot_states[robot_id][j+1]['partials'],
                            next_partialw=robot_states[robot_id][j+1]['partialw'],
                            next_done=(j == len(robot_actions[robot_id]) - 1)
                        )
                        robot_memories[robot_id].append(transition)
            
            print(f"Collected data from instance {inst_no:05d}")
            
        except Exception as e:
            print(f"Error processing instance {inst_no:05d}: {e}")
            continue
    
    total_transitions = sum(len(memory) for memory in robot_memories)
    print(f"Total transitions collected: {total_transitions}")
    for i, memory in enumerate(robot_memories):
        print(f"  Robot {i}: {len(memory)} transitions")
    
    return robot_memories


def train_decentralized_system(robot_memories, num_robots, args):
    """Train the decentralized multi-robot system."""
    device = torch.device(args.device)
    
    # Network setup
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    # Create decentralized system
    decentralized_system = MultiRobotDecentralizedSystem(
        in_dim, hid_dim, out_dim, cetypes, num_robots, 8
    ).to(device)
    
    # Create separate optimizers for each robot
    robot_optimizers = []
    robot_schedulers = []
    for robot_id in range(num_robots):
        robot_net = decentralized_system.get_robot_network(robot_id)
        optimizer = torch.optim.Adam(robot_net.parameters(), lr=args.lr, weight_decay=args.weight_decay)
        scheduler = ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=20, min_lr=1e-7)
        robot_optimizers.append(optimizer)
        robot_schedulers.append(scheduler)
    
    print(f"Starting decentralized training for {args.steps} steps...")
    
    # Training metrics
    training_losses = [[] for _ in range(num_robots)]
    training_rewards = [[] for _ in range(num_robots)]
    training_steps = []
    
    # Training loop
    for step in range(1, args.steps + 1):
        start_time = time.time()
        
        total_loss = 0.0
        step_rewards = []
        
        # Train each robot separately
        for robot_id in range(num_robots):
            if len(robot_memories[robot_id]) == 0:
                continue
            
            robot_net = decentralized_system.get_robot_network(robot_id)
            robot_net.train()
            
            # Sample batch for this robot
            batch_size = min(args.batch_size, len(robot_memories[robot_id]))
            batch_indices = np.random.choice(len(robot_memories[robot_id]), batch_size, replace=False)
            batch = [robot_memories[robot_id][i] for i in batch_indices]
            
            robot_loss = 0.0
            batch_rewards = []
            
            for transition in batch:
                # Build graph and features for this robot
                unsch_tasks = []
                for task_id in range(1, transition.durs.shape[0] + 1):
                    if task_id not in transition.curr_partialw:
                        unsch_tasks.append(task_id)
                
                if len(unsch_tasks) == 0:
                    continue
                
                # Build heterogeneous graph
                g = build_hetgraph(
                    transition.curr_g,
                    transition.durs.shape[0],
                    num_robots,
                    transition.durs.astype(np.float32),
                    6,  # map_width
                    np.array(transition.locs, dtype=np.int64),
                    1,  # loc_dist_threshold
                    transition.curr_partials,
                    np.array(unsch_tasks, dtype=np.int32),
                    robot_id,  # This robot's perspective
                    np.array(unsch_tasks, dtype=np.int32)
                ).to(device)
                
                # Build features
                feat_dict = hetgraph_node_helper(
                    transition.curr_g.number_of_nodes(),
                    transition.curr_partialw,
                    transition.curr_partials,
                    transition.locs,
                    transition.durs,
                    6,  # map_width
                    num_robots,
                    len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) 
                               for k, v in feat_dict.items()}
                
                # Forward pass with communication
                outputs = decentralized_system.forward_with_communication(
                    robot_id, g, feat_tensors, communication_rounds=1
                )
                q_values = outputs['value']
                confidence = outputs['confidence']
                
                # Prepare targets
                num_actions = len(unsch_tasks)
                if num_actions > 1:
                    offset = 0.01
                    targets = torch.full((num_actions, 1), float(transition.reward_n) - offset,
                                       device=device, dtype=torch.float32)
                    weights = torch.full((num_actions, 1), 0.8 / (num_actions - 1),
                                       device=device, dtype=torch.float32)

                    # Find expert action
                    expert_idx = 0
                    for j, task in enumerate(unsch_tasks):
                        if task == transition.act_task:
                            expert_idx = j
                            break

                    targets[expert_idx, 0] = float(transition.reward_n)
                    weights[expert_idx, 0] = 1.0
                else:
                    targets = torch.tensor([[float(transition.reward_n)]], device=device, dtype=torch.float32)
                    weights = torch.tensor([[1.0]], device=device, dtype=torch.float32)
                
                # Compute loss with confidence weighting
                mse_loss = F.mse_loss(q_values, targets, reduction='none')
                confidence_weighted_loss = (mse_loss * weights * confidence).sum() / batch_size
                
                # Add confidence regularization (encourage high confidence for good decisions)
                confidence_reg = torch.mean((confidence - 0.8) ** 2) * 0.1
                
                loss = confidence_weighted_loss + confidence_reg
                robot_loss += loss
                
                batch_rewards.append(float(transition.reward_n))
            
            # Backward pass for this robot
            robot_optimizers[robot_id].zero_grad()
            robot_loss.backward()
            utils.clip_grad_norm_(robot_net.parameters(), max_norm=1.0)
            robot_optimizers[robot_id].step()
            
            # Update learning rate
            robot_schedulers[robot_id].step(robot_loss.item())
            
            total_loss += robot_loss.item()
            step_rewards.extend(batch_rewards)
            
            # Track metrics for this robot
            training_losses[robot_id].append(robot_loss.item())
            training_rewards[robot_id].append(np.mean(batch_rewards) if batch_rewards else 0.0)
        
        # Logging
        end_time = time.time()
        avg_reward = np.mean(step_rewards) if step_rewards else 0.0
        training_steps.append(step)
        
        print(f"[Step {step:4d}] Total Loss: {total_loss:.6f}, "
              f"Avg Reward: {avg_reward:.4f}, Time: {end_time - start_time:.2f}s")
        
        # Save checkpoint
        if step % args.checkpoint_interval == 0:
            checkpoint_path = os.path.join(args.cpsave, f"decentralized_checkpoint_{step:05d}.tar")
            
            # Save all robot networks
            robot_states = {}
            robot_optimizer_states = {}
            for robot_id in range(num_robots):
                robot_states[f'robot_{robot_id}'] = decentralized_system.get_robot_network(robot_id).state_dict()
                robot_optimizer_states[f'robot_{robot_id}'] = robot_optimizers[robot_id].state_dict()
            
            torch.save({
                'step': step,
                'robot_networks': robot_states,
                'robot_optimizers': robot_optimizer_states,
                'num_robots': num_robots,
                'alpha': args.alpha,
                'beta': args.beta,
                'loss': total_loss
            }, checkpoint_path)
            print(f"Decentralized checkpoint saved: {checkpoint_path}")
    
    return decentralized_system, training_steps, training_losses, training_rewards


def main():
    parser = argparse.ArgumentParser(description="Decentralized multi-objective SSAN training")
    parser.add_argument("--path-to-train", default="./problem_instances/constraints", type=str)
    parser.add_argument("--num-robots", type=int, default=2, help="Number of robots")
    parser.add_argument("--train-start-no", type=int, default=1, help="Start instance number")
    parser.add_argument("--train-end-no", type=int, default=1000, help="End instance number")
    parser.add_argument("--steps", type=int, default=500, help="Training steps")
    parser.add_argument("--batch-size", type=int, default=8, help="Batch size per robot")
    parser.add_argument("--lr", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--weight-decay", type=float, default=1e-6, help="Weight decay")
    parser.add_argument("--alpha", type=float, default=0.5, help="Weight for makespan objective")
    parser.add_argument("--beta", type=float, default=0.5, help="Weight for workload balance objective")
    parser.add_argument("--checkpoint-interval", type=int, default=50, help="Checkpoint save interval")
    parser.add_argument("--cpsave", default="./cp_decentralized", help="Checkpoint save directory")
    parser.add_argument("--device", default="cpu")
    
    args = parser.parse_args()
    
    # Setup
    os.makedirs(args.cpsave, exist_ok=True)
    
    print(f"Decentralized multi-objective training with α={args.alpha}, β={args.beta}")
    print(f"Training on instances {args.train_start_no} to {args.train_end_no}")
    
    # Collect training data for each robot
    print("Collecting decentralized training data...")
    robot_memories = collect_decentralized_data(
        args.path_to_train, 
        args.train_start_no, 
        args.train_end_no,
        args.num_robots,
        args.alpha,
        args.beta
    )
    
    total_data = sum(len(memory) for memory in robot_memories)
    if total_data == 0:
        print("No training data collected. Exiting.")
        return
    
    # Train decentralized system
    decentralized_system, steps, losses, rewards = train_decentralized_system(
        robot_memories, args.num_robots, args
    )
    
    print("Decentralized multi-objective training completed!")


if __name__ == "__main__":
    main()

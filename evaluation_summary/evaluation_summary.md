# Multi-Objective MRC Scheduling Evaluation Summary

Generated on: 2025-06-05 17:17:56

## Evaluation Overview

- **CSV Results Directory**: `./csv_outputs_multi_objective`
- **Performance Analysis**: `./evaluation_performance`
- **Trade-off Analysis**: `./evaluation_tradeoffs`
- **General Analysis**: `./evaluation_general`

## Key Analysis Components

### 1. Multi-Objective Performance Evaluation
- Comprehensive statistical analysis
- Pareto dominance analysis
- Hypervolume indicators
- Statistical significance tests
- Multi-dimensional performance ranking
- Radar charts and distribution plots

### 2. Trade-off Analysis
- Makespan vs workload balance trade-offs
- Global Pareto front identification
- Solution diversity and efficiency metrics
- Trade-off ratio calculations
- Quality heatmaps and efficiency plots

### 3. Generated Outputs

## How to Use the Results

1. **Start with Performance Analysis**: Review `model_ranking.csv` and `detailed_summary_statistics.csv`
2. **Examine Trade-offs**: Check `trade_off_metrics.csv` and `global_pareto_front.csv`
3. **View Visualizations**: Open PNG files for comprehensive plots
4. **Statistical Validation**: Review statistical test results for significance
5. **Decision Support**: Use Pareto front analysis for model selection

## Key Metrics Explained

- **Feasibility Rate**: Percentage of instances solved successfully
- **Makespan**: Total completion time (lower is better)
- **Workload Balance**: Standard deviation of task distribution (lower is better)
- **Pareto Efficiency**: Percentage of non-dominated solutions
- **Hypervolume**: Quality indicator for multi-objective optimization
- **Trade-off Ratio**: Balance improvement per makespan unit increase

---
*This report was generated automatically by the multi-objective evaluation pipeline.*

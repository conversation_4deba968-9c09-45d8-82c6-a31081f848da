#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
example_simulation.py

Simple example demonstrating how to use the MiniGrid simulation with your trained decentralized model.
"""

import os
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt

# Add current directory to path to import modules
sys.path.append('.')

from run_decentralized_simulation import DecentralizedGridSimulator
from minigrid_environment import GridWorld, GridWorldVisualizer


def demo_basic_simulation():
    """Demonstrate basic simulation functionality."""
    print("=== Basic Grid World Demo ===")
    
    # Create a simple grid world
    grid_world = GridWorld(grid_size=8, num_robots=2, num_goals=4)
    visualizer = GridWorldVisualizer(grid_world)
    
    print(f"Created {grid_world.grid_size}×{grid_world.grid_size} grid world")
    print(f"Robots: {grid_world.num_robots}, Goals: {grid_world.num_goals}")
    print(f"Robot positions: {grid_world.robot_positions}")
    print(f"Goal positions: {grid_world.goal_positions}")
    
    # Show initial state
    plt.figure(figsize=(8, 8))
    visualizer.render(show_paths=False)
    plt.title("Initial Grid World State")
    plt.savefig("initial_state.png", dpi=150, bbox_inches='tight')
    plt.show()
    
    # Simulate some random movements
    print("\nSimulating random movements...")
    for step in range(10):
        for robot_id in range(grid_world.num_robots):
            action = np.random.randint(0, 5)  # Random action
            grid_world.move_robot(robot_id, action)
        
        if step % 3 == 0:
            plt.figure(figsize=(8, 8))
            visualizer.render()
            plt.title(f"Step {step}")
            plt.savefig(f"step_{step}.png", dpi=150, bbox_inches='tight')
            plt.show()
    
    print(f"Goals visited: {len(grid_world.visited_goals)}/{grid_world.num_goals}")


def demo_with_trained_model(model_path: str):
    """Demonstrate simulation with a trained decentralized model."""
    print(f"\n=== Simulation with Trained Model ===")
    print(f"Model: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        print("Please train a model first using decentralized_multi_objective_train.py")
        return
    
    device = torch.device('cpu')
    
    # Create simulator
    simulator = DecentralizedGridSimulator(
        model_path=model_path,
        device=device,
        grid_size=10,
        num_robots=2,
        num_goals=5
    )
    
    print("Running simulation with trained model...")
    
    # Run simulation
    results = simulator.run_simulation(
        max_steps=100,
        visualization=True,
        save_frames=False,
        communication_rounds=2
    )
    
    # Print results
    print("\n=== Simulation Results ===")
    print(f"Goals completed: {results['goals_completed']}/{simulator.num_goals}")
    print(f"Completion rate: {results['completion_rate']*100:.1f}%")
    print(f"Total steps: {results['total_steps']}")
    print(f"Total distance: {results['total_distance']}")
    print(f"Workload balance: {results['workload_balance']:.3f}")
    print(f"Robot distances: {results['robot_distances']}")


def create_sample_scenarios():
    """Create and save sample scenario configurations."""
    scenarios = [
        {
            'name': 'Quick_Test',
            'grid_size': 6,
            'num_robots': 2,
            'num_goals': 3,
            'max_steps': 50,
            'communication_rounds': 1
        },
        {
            'name': 'Standard_Test',
            'grid_size': 10,
            'num_robots': 3,
            'num_goals': 6,
            'max_steps': 150,
            'communication_rounds': 2
        },
        {
            'name': 'Challenge_Test',
            'grid_size': 12,
            'num_robots': 4,
            'num_goals': 10,
            'max_steps': 250,
            'communication_rounds': 3
        }
    ]
    
    import json
    with open('sample_scenarios.json', 'w') as f:
        json.dump(scenarios, f, indent=2)
    
    print("Sample scenarios saved to 'sample_scenarios.json'")
    return scenarios


def main():
    print("MiniGrid Simulation Example")
    print("=" * 40)
    
    # Demo 1: Basic grid world functionality
    demo_basic_simulation()
    
    # Demo 2: Check for trained model and run simulation
    possible_model_paths = [
        "./cp_decentralized/decentralized_checkpoint_00500.tar",
        "./cp_decentralized/decentralized_checkpoint_00200.tar",
        "./cp_decentralized/decentralized_checkpoint_00100.tar"
    ]
    
    model_found = False
    for model_path in possible_model_paths:
        if os.path.exists(model_path):
            demo_with_trained_model(model_path)
            model_found = True
            break
    
    if not model_found:
        print("\n=== No Trained Model Found ===")
        print("To run simulation with a trained model:")
        print("1. First train a decentralized model:")
        print("   python decentralized_multi_objective_train.py \\")
        print("     --path-to-train ./problem_instances/constraints \\")
        print("     --num-robots 2 --steps 200")
        print("")
        print("2. Then run the simulation:")
        print("   python run_decentralized_simulation.py \\")
        print("     --model-path ./cp_decentralized/decentralized_checkpoint_00200.tar \\")
        print("     --grid-size 10 --num-robots 2 --num-goals 5")
        print("")
        print("3. Or run batch experiments:")
        print("   python batch_grid_simulation.py \\")
        print("     --model-path ./cp_decentralized/decentralized_checkpoint_00200.tar \\")
        print("     --num-runs 3")
    
    # Demo 3: Create sample scenarios
    print("\n=== Creating Sample Scenarios ===")
    scenarios = create_sample_scenarios()
    for i, scenario in enumerate(scenarios):
        print(f"{i+1}. {scenario['name']}: {scenario['grid_size']}×{scenario['grid_size']} grid, "
              f"{scenario['num_robots']} robots, {scenario['num_goals']} goals")
    
    print("\n=== Usage Examples ===")
    print("1. Single simulation:")
    print("   python run_decentralized_simulation.py --model-path MODEL_PATH")
    print("")
    print("2. Batch experiments:")
    print("   python batch_grid_simulation.py --model-path MODEL_PATH --num-runs 5")
    print("")
    print("3. Custom scenarios:")
    print("   python batch_grid_simulation.py --model-path MODEL_PATH --scenarios-file sample_scenarios.json")


if __name__ == "__main__":
    # Set random seed for reproducible results
    np.random.seed(42)
    torch.manual_seed(42)
    
    main()

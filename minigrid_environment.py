#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
minigrid_environment.py

MiniGrid-based simulation environment for decentralized multi-robot task scheduling.
N autonomous agents cooperatively discover and visit M goals in an L×L grid.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import random
from typing import List, Tuple, Dict, Optional
import time


class GridWorld:
    """
    Grid-based environment for multi-robot task scheduling simulation.
    """
    
    def __init__(self, grid_size: int = 10, num_robots: int = 2, num_goals: int = 5):
        self.grid_size = grid_size
        self.num_robots = num_robots
        self.num_goals = num_goals
        
        # Grid state: 0=empty, 1=obstacle, 2=goal, 3=robot
        self.grid = np.zeros((grid_size, grid_size), dtype=int)
        
        # Robot and goal positions
        self.robot_positions = []
        self.goal_positions = []
        self.visited_goals = set()
        
        # Task assignments (goal_id -> robot_id)
        self.task_assignments = {}
        
        # Movement history for visualization
        self.robot_paths = [[] for _ in range(num_robots)]
        
        # Initialize environment
        self._initialize_environment()
    
    def _initialize_environment(self):
        """Initialize robot and goal positions randomly."""
        # Clear grid
        self.grid.fill(0)
        
        # Place obstacles (optional - can be added for complexity)
        num_obstacles = max(1, self.grid_size // 3)
        for _ in range(num_obstacles):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:
                    self.grid[x, y] = 1  # Obstacle
                    break
        
        # Place robots
        self.robot_positions = []
        for robot_id in range(self.num_robots):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:
                    self.robot_positions.append((x, y))
                    self.grid[x, y] = 3  # Robot
                    self.robot_paths[robot_id] = [(x, y)]
                    break
        
        # Place goals
        self.goal_positions = []
        for goal_id in range(self.num_goals):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:
                    self.goal_positions.append((x, y))
                    self.grid[x, y] = 2  # Goal
                    break
        
        self.visited_goals = set()
        self.task_assignments = {}
    
    def get_robot_observation(self, robot_id: int, vision_range: int = 3) -> Dict:
        """
        Get local observation for a specific robot.
        
        Args:
            robot_id: ID of the robot
            vision_range: How far the robot can see
            
        Returns:
            Dictionary containing local observation
        """
        robot_pos = self.robot_positions[robot_id]
        x, y = robot_pos
        
        # Local grid observation
        local_grid = np.zeros((2*vision_range+1, 2*vision_range+1), dtype=int)
        
        for dx in range(-vision_range, vision_range+1):
            for dy in range(-vision_range, vision_range+1):
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.grid_size and 0 <= ny < self.grid_size:
                    local_grid[dx+vision_range, dy+vision_range] = self.grid[nx, ny]
                else:
                    local_grid[dx+vision_range, dy+vision_range] = -1  # Out of bounds
        
        # Visible goals
        visible_goals = []
        for goal_id, goal_pos in enumerate(self.goal_positions):
            if goal_id not in self.visited_goals:
                gx, gy = goal_pos
                if abs(gx - x) <= vision_range and abs(gy - y) <= vision_range:
                    visible_goals.append({
                        'id': goal_id,
                        'position': goal_pos,
                        'distance': abs(gx - x) + abs(gy - y)  # Manhattan distance
                    })
        
        # Visible other robots
        visible_robots = []
        for other_id, other_pos in enumerate(self.robot_positions):
            if other_id != robot_id:
                ox, oy = other_pos
                if abs(ox - x) <= vision_range and abs(oy - y) <= vision_range:
                    visible_robots.append({
                        'id': other_id,
                        'position': other_pos,
                        'distance': abs(ox - x) + abs(oy - y)
                    })
        
        return {
            'robot_id': robot_id,
            'position': robot_pos,
            'local_grid': local_grid,
            'visible_goals': visible_goals,
            'visible_robots': visible_robots,
            'assigned_goal': self.task_assignments.get(robot_id, None)
        }
    
    def move_robot(self, robot_id: int, action: int) -> bool:
        """
        Move robot based on action.
        
        Args:
            robot_id: ID of the robot to move
            action: 0=stay, 1=up, 2=right, 3=down, 4=left
            
        Returns:
            True if move was successful, False otherwise
        """
        if robot_id >= len(self.robot_positions):
            return False
        
        x, y = self.robot_positions[robot_id]
        
        # Clear current position
        self.grid[x, y] = 0
        
        # Calculate new position
        if action == 1:  # Up
            new_x, new_y = max(0, x-1), y
        elif action == 2:  # Right
            new_x, new_y = x, min(self.grid_size-1, y+1)
        elif action == 3:  # Down
            new_x, new_y = min(self.grid_size-1, x+1), y
        elif action == 4:  # Left
            new_x, new_y = x, max(0, y-1)
        else:  # Stay (action == 0)
            new_x, new_y = x, y
        
        # Check if new position is valid (not obstacle or another robot)
        if self.grid[new_x, new_y] in [0, 2]:  # Empty or goal
            # Check if there's a goal at this position
            goal_at_position = None
            for goal_id, goal_pos in enumerate(self.goal_positions):
                if goal_pos == (new_x, new_y) and goal_id not in self.visited_goals:
                    goal_at_position = goal_id
                    break
            
            # Update robot position
            self.robot_positions[robot_id] = (new_x, new_y)
            self.robot_paths[robot_id].append((new_x, new_y))
            self.grid[new_x, new_y] = 3  # Robot
            
            # Check if robot reached a goal
            if goal_at_position is not None:
                self.visited_goals.add(goal_at_position)
                print(f"Robot {robot_id} reached goal {goal_at_position} at position {(new_x, new_y)}")
                return True
            
            return True
        else:
            # Invalid move, stay in place
            self.grid[x, y] = 3  # Robot stays
            return False
    
    def assign_task(self, robot_id: int, goal_id: int):
        """Assign a goal to a robot."""
        if goal_id not in self.visited_goals:
            self.task_assignments[robot_id] = goal_id
    
    def get_state(self) -> Dict:
        """Get current state of the environment."""
        return {
            'grid': self.grid.copy(),
            'robot_positions': self.robot_positions.copy(),
            'goal_positions': self.goal_positions.copy(),
            'visited_goals': self.visited_goals.copy(),
            'task_assignments': self.task_assignments.copy(),
            'completion_rate': len(self.visited_goals) / self.num_goals
        }
    
    def is_complete(self) -> bool:
        """Check if all goals have been visited."""
        return len(self.visited_goals) == self.num_goals
    
    def reset(self):
        """Reset the environment."""
        self._initialize_environment()


class GridWorldVisualizer:
    """
    Visualizer for the grid world environment.
    """
    
    def __init__(self, grid_world: GridWorld):
        self.grid_world = grid_world
        self.fig, self.ax = plt.subplots(figsize=(10, 10))
        self.robot_colors = ['red', 'blue', 'green', 'orange', 'purple']
        
    def render(self, show_paths: bool = True, show_assignments: bool = True):
        """Render the current state of the grid world."""
        self.ax.clear()
        
        grid_size = self.grid_world.grid_size
        
        # Draw grid
        for i in range(grid_size + 1):
            self.ax.axhline(i - 0.5, color='black', linewidth=0.5)
            self.ax.axvline(i - 0.5, color='black', linewidth=0.5)
        
        # Draw obstacles
        for x in range(grid_size):
            for y in range(grid_size):
                if self.grid_world.grid[x, y] == 1:  # Obstacle
                    rect = patches.Rectangle((y-0.5, grid_size-x-1.5), 1, 1, 
                                           linewidth=1, edgecolor='black', facecolor='gray')
                    self.ax.add_patch(rect)
        
        # Draw goals
        for goal_id, (gx, gy) in enumerate(self.grid_world.goal_positions):
            if goal_id not in self.grid_world.visited_goals:
                circle = patches.Circle((gy, grid_size-gx-1), 0.3, 
                                      linewidth=2, edgecolor='gold', facecolor='yellow')
                self.ax.add_patch(circle)
                self.ax.text(gy, grid_size-gx-1, str(goal_id), ha='center', va='center', fontsize=8)
            else:
                # Visited goal
                circle = patches.Circle((gy, grid_size-gx-1), 0.2, 
                                      linewidth=1, edgecolor='gray', facecolor='lightgray')
                self.ax.add_patch(circle)
        
        # Draw robot paths
        if show_paths:
            for robot_id, path in enumerate(self.grid_world.robot_paths):
                if len(path) > 1:
                    path_x = [grid_size - pos[0] - 1 for pos in path]
                    path_y = [pos[1] for pos in path]
                    self.ax.plot(path_y, path_x, '--', 
                               color=self.robot_colors[robot_id % len(self.robot_colors)], 
                               alpha=0.5, linewidth=2)
        
        # Draw robots
        for robot_id, (rx, ry) in enumerate(self.grid_world.robot_positions):
            color = self.robot_colors[robot_id % len(self.robot_colors)]
            circle = patches.Circle((ry, grid_size-rx-1), 0.4, 
                                  linewidth=2, edgecolor='black', facecolor=color)
            self.ax.add_patch(circle)
            self.ax.text(ry, grid_size-rx-1, str(robot_id), ha='center', va='center', 
                        fontsize=10, fontweight='bold', color='white')
        
        # Draw task assignments
        if show_assignments:
            for robot_id, goal_id in self.grid_world.task_assignments.items():
                if goal_id is not None and goal_id not in self.grid_world.visited_goals:
                    rx, ry = self.grid_world.robot_positions[robot_id]
                    gx, gy = self.grid_world.goal_positions[goal_id]
                    self.ax.plot([ry, gy], [grid_size-rx-1, grid_size-gx-1], 
                               linestyle=':', color='red', alpha=0.7, linewidth=2)
        
        # Set axis properties
        self.ax.set_xlim(-0.5, grid_size - 0.5)
        self.ax.set_ylim(-0.5, grid_size - 0.5)
        self.ax.set_aspect('equal')
        self.ax.set_title(f'Multi-Robot Grid World - Goals Visited: {len(self.grid_world.visited_goals)}/{self.grid_world.num_goals}')
        
        # Add legend
        legend_elements = []
        for i in range(self.grid_world.num_robots):
            color = self.robot_colors[i % len(self.robot_colors)]
            legend_elements.append(patches.Patch(color=color, label=f'Robot {i}'))
        legend_elements.append(patches.Patch(color='yellow', label='Unvisited Goal'))
        legend_elements.append(patches.Patch(color='lightgray', label='Visited Goal'))
        legend_elements.append(patches.Patch(color='gray', label='Obstacle'))
        
        self.ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))
        
        plt.tight_layout()
        plt.draw()
        plt.pause(0.1)
    
    def save_frame(self, filename: str):
        """Save current frame as image."""
        plt.savefig(filename, dpi=150, bbox_inches='tight')


def create_scheduling_problem_from_grid(grid_world: GridWorld) -> Dict:
    """
    Convert grid world state to scheduling problem format compatible with your models.
    
    Args:
        grid_world: Current grid world state
        
    Returns:
        Dictionary with problem data in your model's expected format
    """
    num_robots = grid_world.num_robots
    unvisited_goals = [i for i in range(grid_world.num_goals) if i not in grid_world.visited_goals]
    num_tasks = len(unvisited_goals)
    
    if num_tasks == 0:
        return None
    
    # Create duration matrix (Manhattan distance + base cost)
    durations = np.zeros((num_tasks, num_robots), dtype=np.float32)
    
    for task_idx, goal_id in enumerate(unvisited_goals):
        goal_pos = grid_world.goal_positions[goal_id]
        for robot_id in range(num_robots):
            robot_pos = grid_world.robot_positions[robot_id]
            # Manhattan distance as duration
            distance = abs(goal_pos[0] - robot_pos[0]) + abs(goal_pos[1] - robot_pos[1])
            durations[task_idx, robot_id] = distance + 1  # +1 base execution cost
    
    # Create locations (goal positions)
    locations = np.array([grid_world.goal_positions[goal_id] for goal_id in unvisited_goals], dtype=np.int32)
    
    # Create deadlines (can be set based on problem requirements)
    deadlines = np.array([[i+1, num_tasks * 2] for i in range(num_tasks)], dtype=np.int32)

    # Create wait constraints (format: [ti, tj, wait_time])
    # For grid world, we'll use minimal wait constraints
    if num_tasks >= 2:
        wait_constraints = np.array([[1, 2, 0]], dtype=np.int32)  # Minimal constraint between tasks 1 and 2
    else:
        wait_constraints = np.array([[1, 1, 0]], dtype=np.int32)  # Self-constraint for single task
    
    return {
        'durations': durations,
        'locations': locations,
        'deadlines': deadlines,
        'wait_constraints': wait_constraints,
        'unvisited_goals': unvisited_goals,
        'num_tasks': num_tasks,
        'num_robots': num_robots
    }

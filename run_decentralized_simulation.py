#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run_decentralized_simulation.py

Run MiniGrid simulation with trained decentralized multi-objective model.
Loads your trained model and simulates N robots cooperatively discovering and visiting M goals.
"""

import os
import argparse
import numpy as np
import torch
import time
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

# Import your existing components
from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance

# Import new grid world components
from minigrid_environment import GridWorld, GridWorldVisualizer, create_scheduling_problem_from_grid


class DecentralizedGridSimulator:
    """
    Simulator that runs decentralized robots in a grid world using trained models.
    """
    
    def __init__(self, model_path: str, device: torch.device, grid_size: int = 10, 
                 num_robots: int = 2, num_goals: int = 5):
        self.device = device
        self.grid_size = grid_size
        self.num_robots = num_robots
        self.num_goals = num_goals
        
        # Load trained decentralized model
        self.decentralized_system, self.alpha, self.beta = self._load_model(model_path)
        
        # Create grid world environment
        self.grid_world = GridWorld(grid_size, num_robots, num_goals)
        self.visualizer = GridWorldVisualizer(self.grid_world)
        
        # Simulation statistics
        self.step_count = 0
        self.total_distance_traveled = 0
        self.task_completion_times = []
        
        print(f"Initialized simulation: {grid_size}×{grid_size} grid, {num_robots} robots, {num_goals} goals")
        print(f"Model parameters: α={self.alpha:.3f}, β={self.beta:.3f}")
    
    def _load_model(self, model_path: str) -> Tuple:
        """Load the trained decentralized model."""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        num_robots = checkpoint['num_robots']
        alpha = checkpoint.get('alpha', 0.5)
        beta = checkpoint.get('beta', 0.5)
        
        # Network architecture (same as training)
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]
        
        # Create decentralized system
        decentralized_system = MultiRobotDecentralizedSystem(
            in_dim, hid_dim, out_dim, cetypes, num_robots, 8
        ).to(self.device)
        
        # Load robot network states
        robot_networks = checkpoint['robot_networks']
        for robot_id in range(num_robots):
            robot_key = f'robot_{robot_id}'
            if robot_key in robot_networks:
                robot_net = decentralized_system.get_robot_network(robot_id)
                robot_net.load_state_dict(robot_networks[robot_key])
                robot_net.eval()
        
        print(f"Loaded decentralized model with {num_robots} robots")
        return decentralized_system, alpha, beta
    
    def _create_mock_scheduling_env(self, problem_data: Dict) -> SchedulingEnv:
        """Create a mock scheduling environment for the neural network."""
        import tempfile
        import os

        # Create temporary directory and files
        temp_dir = tempfile.mkdtemp()
        temp_prefix = os.path.join(temp_dir, "temp_grid_problem")

        try:
            # Save problem data to temporary files
            np.savetxt(f"{temp_prefix}_dur.txt", problem_data['durations'], fmt='%d')
            np.savetxt(f"{temp_prefix}_ddl.txt", problem_data['deadlines'], fmt='%d')
            np.savetxt(f"{temp_prefix}_wait.txt", problem_data['wait_constraints'], fmt='%d')
            np.savetxt(f"{temp_prefix}_loc.txt", problem_data['locations'], fmt='%d')

            # Create scheduling environment
            env = SchedulingEnv(temp_prefix)
            env.set_multi_objective_params(alpha=self.alpha, beta=self.beta)

            return env
        except Exception as e:
            # Clean up temporary files on error
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            raise e
    
    def _get_robot_decisions(self, problem_data: Dict, communication_rounds: int = 2) -> Dict:
        """Get decisions from all robots using the decentralized model."""
        if problem_data is None or problem_data['num_tasks'] == 0:
            return {}
        
        # Create mock scheduling environment
        env = self._create_mock_scheduling_env(problem_data)
        
        robot_decisions = {}
        
        # Each robot makes a decision
        for robot_id in range(self.num_robots):
            try:
                # Get unscheduled tasks
                unsch_tasks = list(range(1, problem_data['num_tasks'] + 1))
                
                if len(unsch_tasks) == 0:
                    continue
                
                # Build graph from this robot's perspective
                g = build_hetgraph(
                    env.halfDG,
                    problem_data['num_tasks'],
                    self.num_robots,
                    problem_data['durations'].astype(np.float32),
                    6,  # map_width
                    np.array(problem_data['locations'], dtype=np.int64),
                    1,  # loc_dist_threshold
                    env.partials,
                    np.array(unsch_tasks, dtype=np.int64),
                    robot_id,
                    np.array(unsch_tasks, dtype=np.int64)
                ).to(self.device)
                
                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(),
                    env.partialw,
                    env.partials,
                    problem_data['locations'],
                    problem_data['durations'],
                    6,  # map_width
                    self.num_robots,
                    len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=self.device, dtype=torch.float32) 
                               for k, v in feat_dict.items()}
                
                # Forward pass with communication
                with torch.no_grad():
                    outputs = self.decentralized_system.forward_with_communication(
                        robot_id, g, feat_tensors, communication_rounds=communication_rounds
                    )
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                    confidence = outputs['confidence'].cpu().numpy().reshape(-1)
                
                # Find best task for this robot
                best_idx = np.argmax(q_values)
                best_task_local = int(unsch_tasks[best_idx])
                best_goal = problem_data['unvisited_goals'][best_task_local - 1]
                
                robot_decisions[robot_id] = {
                    'goal_id': best_goal,
                    'q_value': float(q_values[best_idx]),
                    'confidence': float(confidence[best_idx]),
                    'local_task_id': best_task_local
                }
                
            except Exception as e:
                print(f"Error getting decision for robot {robot_id}: {e}")
                robot_decisions[robot_id] = {
                    'goal_id': None,
                    'q_value': -float('inf'),
                    'confidence': 0.0,
                    'local_task_id': None
                }
        
        return robot_decisions
    
    def _plan_path_to_goal(self, robot_id: int, goal_id: int) -> List[int]:
        """Plan a simple path from robot to goal using A* or greedy approach."""
        robot_pos = self.grid_world.robot_positions[robot_id]
        goal_pos = self.grid_world.goal_positions[goal_id]
        
        # Simple greedy path planning
        path_actions = []
        current_pos = robot_pos
        
        while current_pos != goal_pos:
            rx, ry = current_pos
            gx, gy = goal_pos
            
            # Move towards goal (greedy)
            if gx < rx:  # Move up
                path_actions.append(1)
                current_pos = (rx - 1, ry)
            elif gx > rx:  # Move down
                path_actions.append(3)
                current_pos = (rx + 1, ry)
            elif gy < ry:  # Move left
                path_actions.append(4)
                current_pos = (rx, ry - 1)
            elif gy > ry:  # Move right
                path_actions.append(2)
                current_pos = (rx, ry + 1)
            
            # Safety check to avoid infinite loops
            if len(path_actions) > self.grid_size * 2:
                break
        
        return path_actions
    
    def run_simulation(self, max_steps: int = 200, visualization: bool = True, 
                      save_frames: bool = False, communication_rounds: int = 2) -> Dict:
        """
        Run the complete simulation.
        
        Args:
            max_steps: Maximum number of simulation steps
            visualization: Whether to show visualization
            save_frames: Whether to save frames as images
            communication_rounds: Number of communication rounds for robots
            
        Returns:
            Dictionary with simulation results
        """
        print("Starting decentralized grid simulation...")
        
        if visualization:
            plt.ion()
            self.visualizer.render()
            plt.show()
        
        robot_plans = {i: [] for i in range(self.num_robots)}
        step_results = []
        
        for step in range(max_steps):
            self.step_count = step
            
            # Check if simulation is complete
            if self.grid_world.is_complete():
                print(f"All goals completed in {step} steps!")
                break
            
            # Get current problem state
            problem_data = create_scheduling_problem_from_grid(self.grid_world)
            
            if problem_data is None or problem_data['num_tasks'] == 0:
                break
            
            # Get robot decisions using decentralized model
            robot_decisions = self._get_robot_decisions(problem_data, communication_rounds)
            
            # Assign tasks based on confidence (conflict resolution)
            assigned_goals = set()
            for robot_id in sorted(robot_decisions.keys(), 
                                 key=lambda r: robot_decisions[r]['confidence'], reverse=True):
                decision = robot_decisions[robot_id]
                goal_id = decision['goal_id']
                
                if goal_id is not None and goal_id not in assigned_goals:
                    self.grid_world.assign_task(robot_id, goal_id)
                    assigned_goals.add(goal_id)
                    
                    # Plan path to goal
                    if robot_id not in robot_plans or len(robot_plans[robot_id]) == 0:
                        robot_plans[robot_id] = self._plan_path_to_goal(robot_id, goal_id)
                    
                    print(f"Step {step}: Robot {robot_id} assigned to goal {goal_id} "
                          f"(confidence: {decision['confidence']:.3f})")
            
            # Execute robot movements
            for robot_id in range(self.num_robots):
                if robot_id in robot_plans and len(robot_plans[robot_id]) > 0:
                    action = robot_plans[robot_id].pop(0)
                    success = self.grid_world.move_robot(robot_id, action)
                    
                    if not success:
                        # If movement failed, replan
                        assigned_goal = self.grid_world.task_assignments.get(robot_id)
                        if assigned_goal is not None:
                            robot_plans[robot_id] = self._plan_path_to_goal(robot_id, assigned_goal)
                else:
                    # No plan, stay in place
                    self.grid_world.move_robot(robot_id, 0)
            
            # Record step results
            state = self.grid_world.get_state()
            step_results.append({
                'step': step,
                'completion_rate': state['completion_rate'],
                'visited_goals': len(state['visited_goals']),
                'robot_decisions': robot_decisions
            })
            
            # Visualization
            if visualization:
                self.visualizer.render()
                if save_frames:
                    self.visualizer.save_frame(f"frame_{step:04d}.png")
                time.sleep(0.5)  # Pause for visualization
            
            print(f"Step {step}: {len(self.grid_world.visited_goals)}/{self.num_goals} goals completed")
        
        # Calculate final statistics
        final_stats = self._calculate_statistics(step_results)
        
        if visualization:
            plt.ioff()
            plt.show()
        
        return final_stats
    
    def _calculate_statistics(self, step_results: List[Dict]) -> Dict:
        """Calculate simulation statistics."""
        total_steps = len(step_results)
        completion_time = total_steps
        
        # Find when each goal was completed
        goal_completion_times = {}
        for step_data in step_results:
            if step_data['step'] == 0:
                continue
            prev_visited = step_results[step_data['step'] - 1]['visited_goals'] if step_data['step'] > 0 else 0
            curr_visited = step_data['visited_goals']
            if curr_visited > prev_visited:
                goal_completion_times[curr_visited - 1] = step_data['step']
        
        # Calculate robot workload balance
        robot_distances = [len(path) for path in self.grid_world.robot_paths]
        workload_balance = np.std(robot_distances) if len(robot_distances) > 1 else 0.0
        
        stats = {
            'total_steps': total_steps,
            'goals_completed': len(self.grid_world.visited_goals),
            'completion_rate': len(self.grid_world.visited_goals) / self.num_goals,
            'completion_time': completion_time,
            'robot_distances': robot_distances,
            'total_distance': sum(robot_distances),
            'workload_balance': workload_balance,
            'goal_completion_times': goal_completion_times,
            'average_completion_time': np.mean(list(goal_completion_times.values())) if goal_completion_times else total_steps
        }
        
        return stats


def main():
    parser = argparse.ArgumentParser(description="Run decentralized grid simulation")
    parser.add_argument("--model-path", required=True, help="Path to trained decentralized model")
    parser.add_argument("--grid-size", type=int, default=10, help="Grid size (L×L)")
    parser.add_argument("--num-robots", type=int, default=2, help="Number of robots (N)")
    parser.add_argument("--num-goals", type=int, default=5, help="Number of goals (M)")
    parser.add_argument("--max-steps", type=int, default=200, help="Maximum simulation steps")
    parser.add_argument("--communication-rounds", type=int, default=2, help="Communication rounds")
    parser.add_argument("--no-visualization", action="store_true", help="Disable visualization")
    parser.add_argument("--save-frames", action="store_true", help="Save frames as images")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")
    
    args = parser.parse_args()
    
    # Set random seed
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    device = torch.device(args.device)
    
    print("Decentralized Multi-Robot Grid Simulation")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Grid: {args.grid_size}×{args.grid_size}")
    print(f"Robots: {args.num_robots}, Goals: {args.num_goals}")
    print(f"Communication rounds: {args.communication_rounds}")
    
    # Create and run simulation
    simulator = DecentralizedGridSimulator(
        args.model_path, device, args.grid_size, args.num_robots, args.num_goals
    )
    
    results = simulator.run_simulation(
        max_steps=args.max_steps,
        visualization=not args.no_visualization,
        save_frames=args.save_frames,
        communication_rounds=args.communication_rounds
    )
    
    # Print results
    print("\n" + "=" * 50)
    print("SIMULATION RESULTS")
    print("=" * 50)
    print(f"Goals completed: {results['goals_completed']}/{args.num_goals} ({results['completion_rate']*100:.1f}%)")
    print(f"Total steps: {results['total_steps']}")
    print(f"Total distance traveled: {results['total_distance']}")
    print(f"Workload balance (std): {results['workload_balance']:.3f}")
    print(f"Robot distances: {results['robot_distances']}")
    
    if results['goal_completion_times']:
        print(f"Average goal completion time: {results['average_completion_time']:.1f} steps")
        print("Goal completion times:", results['goal_completion_times'])


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
simple_grid_demo.py

Simple demonstration of the grid world simulation without visualization.
"""

import numpy as np
import sys
import os

# Add current directory to path
sys.path.append('.')

from minigrid_environment import GridWorld, create_scheduling_problem_from_grid


def demo_grid_world():
    """Demonstrate basic grid world functionality."""
    print("=== Grid World Demo ===")
    
    # Create grid world
    grid_world = GridWorld(grid_size=8, num_robots=2, num_goals=4)
    
    print(f"Created {grid_world.grid_size}×{grid_world.grid_size} grid world")
    print(f"Robots: {grid_world.num_robots}, Goals: {grid_world.num_goals}")
    print(f"Robot positions: {grid_world.robot_positions}")
    print(f"Goal positions: {grid_world.goal_positions}")
    
    # Show initial grid state
    print("\nInitial grid state:")
    for i in range(grid_world.grid_size):
        row = ""
        for j in range(grid_world.grid_size):
            cell = grid_world.grid[i, j]
            if cell == 0:
                row += ". "  # Empty
            elif cell == 1:
                row += "# "  # Obstacle
            elif cell == 2:
                row += "G "  # Goal
            elif cell == 3:
                row += "R "  # Robot
        print(row)
    
    # Test problem conversion
    problem_data = create_scheduling_problem_from_grid(grid_world)
    if problem_data:
        print(f"\nProblem conversion successful:")
        print(f"  Tasks: {problem_data['num_tasks']}")
        print(f"  Duration matrix shape: {problem_data['durations'].shape}")
        print(f"  Duration matrix:\n{problem_data['durations']}")
        print(f"  Goal positions: {problem_data['locations']}")
    else:
        print("No unvisited goals to convert")
    
    # Simulate random movements
    print("\nSimulating random robot movements...")
    for step in range(15):
        print(f"\nStep {step + 1}:")
        
        # Move each robot randomly
        for robot_id in range(grid_world.num_robots):
            action = np.random.randint(0, 5)  # 0=stay, 1=up, 2=right, 3=down, 4=left
            old_pos = grid_world.robot_positions[robot_id]
            success = grid_world.move_robot(robot_id, action)
            new_pos = grid_world.robot_positions[robot_id]
            
            action_names = ["stay", "up", "right", "down", "left"]
            print(f"  Robot {robot_id}: {action_names[action]} from {old_pos} to {new_pos} ({'success' if success else 'blocked'})")
        
        # Check goal completion
        print(f"  Goals visited: {len(grid_world.visited_goals)}/{grid_world.num_goals}")
        if len(grid_world.visited_goals) > 0:
            print(f"  Visited goal IDs: {list(grid_world.visited_goals)}")
        
        # Stop if all goals are visited
        if grid_world.is_complete():
            print(f"\n🎉 All goals completed in {step + 1} steps!")
            break
    
    # Final statistics
    print(f"\nFinal Results:")
    print(f"  Goals completed: {len(grid_world.visited_goals)}/{grid_world.num_goals}")
    print(f"  Completion rate: {len(grid_world.visited_goals)/grid_world.num_goals*100:.1f}%")
    
    # Robot path lengths
    for robot_id, path in enumerate(grid_world.robot_paths):
        print(f"  Robot {robot_id} path length: {len(path) - 1} moves")  # -1 because path includes starting position


def demo_scheduling_integration():
    """Demonstrate integration with scheduling problem format."""
    print("\n=== Scheduling Integration Demo ===")
    
    # Create grid world
    grid_world = GridWorld(grid_size=6, num_robots=2, num_goals=3)
    
    print(f"Grid world: {grid_world.grid_size}×{grid_world.grid_size}")
    print(f"Robot positions: {grid_world.robot_positions}")
    print(f"Goal positions: {grid_world.goal_positions}")
    
    # Convert to scheduling problem multiple times as goals are visited
    step = 0
    while not grid_world.is_complete() and step < 20:
        step += 1
        print(f"\nStep {step}:")
        
        # Convert current state to scheduling problem
        problem_data = create_scheduling_problem_from_grid(grid_world)
        
        if problem_data is None:
            print("  No more goals to assign")
            break
        
        print(f"  Unvisited goals: {problem_data['unvisited_goals']}")
        print(f"  Duration matrix:\n{problem_data['durations']}")
        
        # Simple greedy assignment: assign closest goal to each robot
        assignments = {}
        for robot_id in range(grid_world.num_robots):
            if len(problem_data['unvisited_goals']) > robot_id:
                # Find closest goal for this robot
                min_distance = float('inf')
                best_goal = None
                
                for i, goal_id in enumerate(problem_data['unvisited_goals']):
                    distance = problem_data['durations'][i, robot_id]
                    if distance < min_distance:
                        min_distance = distance
                        best_goal = goal_id
                
                if best_goal is not None:
                    assignments[robot_id] = best_goal
                    grid_world.assign_task(robot_id, best_goal)
                    print(f"  Assigned goal {best_goal} to robot {robot_id} (distance: {min_distance})")
        
        # Move robots towards their assigned goals
        for robot_id in range(grid_world.num_robots):
            assigned_goal = grid_world.task_assignments.get(robot_id)
            if assigned_goal is not None and assigned_goal not in grid_world.visited_goals:
                # Simple movement towards goal
                robot_pos = grid_world.robot_positions[robot_id]
                goal_pos = grid_world.goal_positions[assigned_goal]
                
                # Calculate direction to goal
                dx = goal_pos[0] - robot_pos[0]
                dy = goal_pos[1] - robot_pos[1]
                
                # Choose action based on largest distance component
                if abs(dx) > abs(dy):
                    action = 3 if dx > 0 else 1  # down or up
                elif abs(dy) > 0:
                    action = 2 if dy > 0 else 4  # right or left
                else:
                    action = 0  # stay (already at goal)
                
                success = grid_world.move_robot(robot_id, action)
                print(f"  Robot {robot_id} moved towards goal {assigned_goal}: {'success' if success else 'blocked'}")
            else:
                # No assignment, random movement
                action = np.random.randint(0, 5)
                grid_world.move_robot(robot_id, action)
        
        print(f"  Goals visited so far: {len(grid_world.visited_goals)}")
    
    print(f"\nScheduling integration demo completed!")
    print(f"Final completion rate: {len(grid_world.visited_goals)/grid_world.num_goals*100:.1f}%")


def main():
    print("Simple Grid World Demonstration")
    print("=" * 50)
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Run demos
    demo_grid_world()
    demo_scheduling_integration()
    
    print("\n" + "=" * 50)
    print("Demo completed successfully!")
    print("\nNext steps:")
    print("1. Train a decentralized model:")
    print("   python3 decentralized_multi_objective_train.py --path-to-train ./problem_instances/constraints --num-robots 2 --steps 200")
    print("")
    print("2. Run simulation with trained model:")
    print("   python3 run_decentralized_simulation.py --model-path ./cp_decentralized/decentralized_checkpoint_00200.tar --grid-size 10 --num-robots 2 --num-goals 5")
    print("")
    print("3. Run batch experiments:")
    print("   python3 batch_grid_simulation.py --model-path ./cp_decentralized/decentralized_checkpoint_00200.tar --num-runs 5")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_simulation_fix.py

Test script to verify the simulation fix for wait constraints.
"""

import numpy as np
import tempfile
import os
from utils import SchedulingEnv
from minigrid_environment import create_scheduling_problem_from_grid, GridWorld


def test_wait_constraints_format():
    """Test that wait constraints are in the correct format."""
    print("Testing wait constraints format...")
    
    # Create a simple grid world
    grid_world = GridWorld(grid_size=5, num_robots=2, num_goals=3)
    
    # Convert to scheduling problem
    problem_data = create_scheduling_problem_from_grid(grid_world)
    
    if problem_data is None:
        print("No problem data generated")
        return False
    
    print(f"Problem data keys: {problem_data.keys()}")
    print(f"Wait constraints shape: {problem_data['wait_constraints'].shape}")
    print(f"Wait constraints: {problem_data['wait_constraints']}")
    
    # Check format
    wait_shape = problem_data['wait_constraints'].shape
    if len(wait_shape) == 2 and wait_shape[1] == 3:
        print("✓ Wait constraints have correct format (N, 3)")
        return True
    else:
        print("✗ Wait constraints have incorrect format")
        return False


def test_scheduling_env_creation():
    """Test creating SchedulingEnv with grid world data."""
    print("\nTesting SchedulingEnv creation...")
    
    # Create a simple grid world
    grid_world = GridWorld(grid_size=5, num_robots=2, num_goals=3)
    
    # Convert to scheduling problem
    problem_data = create_scheduling_problem_from_grid(grid_world)
    
    if problem_data is None:
        print("No problem data generated")
        return False
    
    # Create temporary files
    temp_dir = tempfile.mkdtemp()
    temp_prefix = os.path.join(temp_dir, "test_problem")
    
    try:
        # Save problem data to temporary files
        np.savetxt(f"{temp_prefix}_dur.txt", problem_data['durations'], fmt='%d')
        np.savetxt(f"{temp_prefix}_ddl.txt", problem_data['deadlines'], fmt='%d')
        np.savetxt(f"{temp_prefix}_wait.txt", problem_data['wait_constraints'], fmt='%d')
        np.savetxt(f"{temp_prefix}_loc.txt", problem_data['locations'], fmt='%d')
        
        print(f"Saved files to: {temp_prefix}")
        print(f"Duration matrix shape: {problem_data['durations'].shape}")
        print(f"Deadlines shape: {problem_data['deadlines'].shape}")
        print(f"Wait constraints shape: {problem_data['wait_constraints'].shape}")
        print(f"Locations shape: {problem_data['locations'].shape}")
        
        # Try to create SchedulingEnv
        env = SchedulingEnv(temp_prefix)
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        
        print("✓ SchedulingEnv created successfully")
        print(f"  Number of tasks: {env.num_tasks}")
        print(f"  Number of robots: {env.num_robots}")
        print(f"  Wait constraints loaded: {env.wait.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating SchedulingEnv: {e}")
        return False
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_mock_model_simulation():
    """Test the simulation without a real trained model."""
    print("\nTesting mock simulation...")
    
    try:
        from minigrid_environment import GridWorld, GridWorldVisualizer
        
        # Create grid world
        grid_world = GridWorld(grid_size=6, num_robots=2, num_goals=3)
        print(f"Created grid world: {grid_world.grid_size}×{grid_world.grid_size}")
        print(f"Robot positions: {grid_world.robot_positions}")
        print(f"Goal positions: {grid_world.goal_positions}")
        
        # Test problem conversion
        problem_data = create_scheduling_problem_from_grid(grid_world)
        if problem_data is None:
            print("✗ No problem data generated")
            return False
        
        print(f"✓ Problem data generated with {problem_data['num_tasks']} tasks")
        
        # Test environment creation
        temp_dir = tempfile.mkdtemp()
        temp_prefix = os.path.join(temp_dir, "mock_problem")
        
        np.savetxt(f"{temp_prefix}_dur.txt", problem_data['durations'], fmt='%d')
        np.savetxt(f"{temp_prefix}_ddl.txt", problem_data['deadlines'], fmt='%d')
        np.savetxt(f"{temp_prefix}_wait.txt", problem_data['wait_constraints'], fmt='%d')
        np.savetxt(f"{temp_prefix}_loc.txt", problem_data['locations'], fmt='%d')
        
        env = SchedulingEnv(temp_prefix)
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        
        print("✓ Mock SchedulingEnv created successfully")
        
        # Test basic robot movements
        for step in range(5):
            for robot_id in range(grid_world.num_robots):
                action = np.random.randint(0, 5)
                success = grid_world.move_robot(robot_id, action)
                if success and len(grid_world.visited_goals) > 0:
                    print(f"  Step {step}: Robot {robot_id} visited a goal!")
        
        print(f"✓ Simulation test completed. Goals visited: {len(grid_world.visited_goals)}")
        
        # Clean up
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"✗ Error in mock simulation: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    print("Testing Simulation Fix")
    print("=" * 30)
    
    # Run tests
    test1_passed = test_wait_constraints_format()
    test2_passed = test_scheduling_env_creation()
    test3_passed = test_mock_model_simulation()
    
    print("\n" + "=" * 30)
    print("Test Results:")
    print(f"Wait constraints format: {'PASS' if test1_passed else 'FAIL'}")
    print(f"SchedulingEnv creation: {'PASS' if test2_passed else 'FAIL'}")
    print(f"Mock simulation: {'PASS' if test3_passed else 'FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n✓ All tests passed! The simulation should work now.")
        print("\nYou can now run:")
        print("  python run_decentralized_simulation.py --model-path YOUR_MODEL_PATH")
    else:
        print("\n✗ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_visualization_colors.py

Test the new color scheme for the grid visualization.
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import sys
import os

# Add current directory to path
sys.path.append('.')

from minigrid_environment import GridWorld, GridWorldVisualizer


def test_color_scheme():
    """Test the new color scheme with black background, green goals, brown obstacles, blue robots."""
    print("Testing new color scheme...")
    
    # Create a grid world with specific layout for testing colors
    grid_world = GridWorld(grid_size=8, num_robots=2, num_goals=3)
    
    # Manually set positions for better color testing
    grid_world.robot_positions = [(1, 1), (6, 6)]
    grid_world.goal_positions = [(2, 3), (4, 5), (6, 2)]
    
    # Clear and rebuild grid
    grid_world.grid.fill(0)
    
    # Add some obstacles manually for testing brown color
    obstacle_positions = [(3, 3), (4, 3), (5, 4)]
    for ox, oy in obstacle_positions:
        grid_world.grid[ox, oy] = 1  # Obstacle
    
    # Add goals
    for goal_id, (gx, gy) in enumerate(grid_world.goal_positions):
        grid_world.grid[gx, gy] = 2  # Goal
    
    # Add robots
    for robot_id, (rx, ry) in enumerate(grid_world.robot_positions):
        grid_world.grid[rx, ry] = 3  # Robot
        grid_world.robot_paths[robot_id] = [(rx, ry)]
    
    # Create visualizer and render
    visualizer = GridWorldVisualizer(grid_world)
    
    # Test initial state
    plt.figure(figsize=(10, 10))
    visualizer.render(show_paths=False, show_assignments=False)
    plt.title("Color Test: Black Grid, Green Goals, Brown Obstacles, Blue Robots", 
              color='white', fontsize=14, fontweight='bold')
    plt.savefig("color_test_initial.png", dpi=150, bbox_inches='tight', facecolor='black')
    print("Saved color_test_initial.png")
    plt.close()
    
    # Test with robot paths
    # Add some path history
    grid_world.robot_paths[0].extend([(1, 2), (1, 3), (2, 3)])  # Robot 0 path to goal
    grid_world.robot_paths[1].extend([(6, 5), (5, 5), (4, 5)])  # Robot 1 path to goal
    
    # Test with task assignments
    grid_world.assign_task(0, 0)  # Robot 0 -> Goal 0
    grid_world.assign_task(1, 1)  # Robot 1 -> Goal 1
    
    plt.figure(figsize=(10, 10))
    visualizer.render(show_paths=True, show_assignments=True)
    plt.title("Color Test: With Paths and Assignments", 
              color='white', fontsize=14, fontweight='bold')
    plt.savefig("color_test_with_paths.png", dpi=150, bbox_inches='tight', facecolor='black')
    print("Saved color_test_with_paths.png")
    plt.close()
    
    # Test with visited goals
    grid_world.visited_goals.add(0)  # Mark goal 0 as visited
    
    plt.figure(figsize=(10, 10))
    visualizer.render(show_paths=True, show_assignments=True)
    plt.title("Color Test: With Visited Goal (Dark Green)", 
              color='white', fontsize=14, fontweight='bold')
    plt.savefig("color_test_visited_goal.png", dpi=150, bbox_inches='tight', facecolor='black')
    print("Saved color_test_visited_goal.png")
    plt.close()
    
    print("Color scheme test completed!")
    print("Generated images:")
    print("  - color_test_initial.png: Basic color scheme")
    print("  - color_test_with_paths.png: With robot paths and task assignments")
    print("  - color_test_visited_goal.png: With visited goal in dark green")
    
    return True


def test_node_error_fix():
    """Test that the node error is fixed by using dynamic map_width calculation."""
    print("\nTesting node error fix...")
    
    try:
        # Create a larger grid world that would trigger the original error
        grid_world = GridWorld(grid_size=12, num_robots=2, num_goals=5)
        
        # Test problem conversion
        from minigrid_environment import create_scheduling_problem_from_grid
        problem_data = create_scheduling_problem_from_grid(grid_world)
        
        if problem_data is None:
            print("No problem data generated")
            return False
        
        print(f"Problem data generated successfully:")
        print(f"  Grid size: {grid_world.grid_size}×{grid_world.grid_size}")
        print(f"  Max location coordinate: {np.max(problem_data['locations'])}")
        print(f"  Duration matrix shape: {problem_data['durations'].shape}")
        
        # Test with the fixed test_models_csv_format functions
        import tempfile
        import shutil
        
        temp_dir = tempfile.mkdtemp()
        temp_prefix = os.path.join(temp_dir, "test_large_grid")
        
        try:
            # Save problem data
            np.savetxt(f"{temp_prefix}_dur.txt", problem_data['durations'], fmt='%d')
            np.savetxt(f"{temp_prefix}_ddl.txt", problem_data['deadlines'], fmt='%d')
            np.savetxt(f"{temp_prefix}_wait.txt", problem_data['wait_constraints'], fmt='%d')
            np.savetxt(f"{temp_prefix}_loc.txt", problem_data['locations'], fmt='%d')
            
            # Test SchedulingEnv creation
            from utils import SchedulingEnv
            env = SchedulingEnv(temp_prefix)
            
            # Calculate dynamic map_width (same as in fixed code)
            max_coord = max(np.max(env.loc[:, 0]), np.max(env.loc[:, 1])) if len(env.loc) > 0 else 6
            map_width = max(6, max_coord + 2)
            
            print(f"  Dynamic map_width calculated: {map_width}")
            print(f"  Location data shape: {env.loc.shape}")
            print(f"  Max coordinate in data: {max_coord}")
            
            print("✓ Node error fix successful - no graph node errors!")
            return True
            
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"✗ Node error fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    print("Testing Visualization Colors and Node Error Fix")
    print("=" * 60)
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Test color scheme
    color_test_passed = test_color_scheme()
    
    # Test node error fix
    node_test_passed = test_node_error_fix()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"Color scheme test: {'PASS' if color_test_passed else 'FAIL'}")
    print(f"Node error fix test: {'PASS' if node_test_passed else 'FAIL'}")
    
    if color_test_passed and node_test_passed:
        print("\n✓ All tests passed!")
        print("\nColor scheme changes:")
        print("  - Grid background: BLACK")
        print("  - Goals: GREEN (unvisited), DARK GREEN (visited)")
        print("  - Obstacles: BROWN")
        print("  - Robots: BLUE (various shades)")
        print("  - Grid lines: WHITE")
        print("  - Task assignments: YELLOW lines")
        print("\nNode error fix:")
        print("  - Dynamic map_width calculation based on actual location data")
        print("  - Prevents 'max ID in data' errors for large grids")
        
        print("\nYou can now run the simulation with:")
        print("  python3 run_decentralized_simulation.py --model-path MODEL_PATH")
    else:
        print("\n✗ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    main()
